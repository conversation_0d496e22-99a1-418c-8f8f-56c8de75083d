import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  Image,
  ActivityIndicator,
} from 'react-native';
import React, { useEffect, useState } from 'react';
import { ScrollView } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Spinner from '../common/spinner';
import { Pressable } from 'react-native';
import { fonts, heightPixel, widthPixel } from '../../styles';
import { getSettings } from '../../../config';
import { useOrders, useCurrentUser, useUser } from '@appmaker-xyz/shopify';
import AsyncStorage from '@react-native-async-storage/async-storage';
export default function Edd({ variants, selectedVariant }) {
  const [pin, setPin] = useState();
  const [pinText, setPinText] = useState('');
  const [loading, setLoading] = useState(false);
  const [finalEdd, setFinalEdd] = useState('');
  const settings = getSettings();

  // Get current user and orders
  const { id: currentUserId } = useCurrentUser({}) || {};
  const { orderList, isLoading: ordersLoading, refetch: refetchOrders } = useOrders({
    limit: 1
  });
console.log("order", orderList)
  // Enhanced order debugging
  console.log("📋 ORDER LIST DEBUG:");
  console.log("Orders loading:", ordersLoading);
  console.log("Order list length:", orderList?.length);
  console.log("Full order data:", JSON.stringify(orderList, null, 2));
  const extractProductVariantId = variants.map((value) => {
    return { variantId: value.node.id.split('/ProductVariant/')[1] };
  });

  // Function to save pincode to local storage with timestamp
  const savePincodeToStorage = async (pincode) => {
    try {
      const pincodeData = {
        pincode: pincode,
        timestamp: new Date().getTime()
      };
      await AsyncStorage.setItem('lastUsedPincode', JSON.stringify(pincodeData));
      console.log('Pincode saved to storage with timestamp:', pincodeData);
    } catch (error) {
      console.error('Error saving pincode to storage:', error);
    }
  };

  // Function to get pincode from local storage
  const getPincodeFromStorage = async () => {
    try {
      const storedData = await AsyncStorage.getItem('lastUsedPincode');
      if (storedData) {
        try {
          const parsedData = JSON.parse(storedData);
          // Return object with pincode and timestamp
          return parsedData;
        } catch (parseError) {
          // Handle old format (just string) - migrate to new format
          console.log('Migrating old pincode format to new format');
          const pincodeData = {
            pincode: storedData,
            timestamp: new Date().getTime()
          };
          await AsyncStorage.setItem('lastUsedPincode', JSON.stringify(pincodeData));
          return pincodeData;
        }
      }
      return null;
    } catch (error) {
      console.error('Error getting pincode from storage:', error);
      return null;
    }
  };

  // Function to extract pincode with priority based on recency (timestamp)
  const prefillPincode = async (forceRefreshOrders = false) => {
    try {
      let pincodeToUse = null;
      let source = '';

      // Optionally refresh orders if requested
      if (forceRefreshOrders && refetchOrders) {
        console.log('🔄 Refreshing orders data...');
        try {
          await refetchOrders();
        } catch (error) {
          console.log('Error refreshing orders:', error);
        }
      }

      // Get stored pincode data (with timestamp)
      const storedPincodeData = await getPincodeFromStorage();
      console.log('💾 Stored pincode data:', storedPincodeData);

      // Get order pincode and its timestamp (if user is logged in)
      let orderPincode = null;
      let orderTimestamp = null;

      if (currentUserId && !ordersLoading && orderList && orderList.length > 0) {
        const latestOrder = orderList[0]?.node;
        console.log('📦 ORDER ANALYSIS:');
        console.log('Total orders found:', orderList.length);
        console.log('Latest order ID:', latestOrder?.id);
        console.log('Latest order number:', latestOrder?.orderNumber || latestOrder?.name);
        console.log('Order processedAt (raw):', latestOrder?.processedAt);
        console.log('Order createdAt (raw):', latestOrder?.createdAt);
        console.log('Shipping address:', latestOrder?.shippingAddress);
        console.log('Shipping address zip:', latestOrder?.shippingAddress?.zip);

        // Check for pincode in different possible field names
        const possiblePincode = latestOrder?.shippingAddress?.zip ||
                               latestOrder?.shippingAddress?.postalCode ||
                               latestOrder?.shippingAddress?.zipCode ||
                               latestOrder?.shippingAddress?.pincode;

        if (possiblePincode) {
          orderPincode = possiblePincode;
          // Convert order processedAt to timestamp for comparison
          orderTimestamp = latestOrder.processedAt ?
            new Date(latestOrder.processedAt).getTime() :
            (latestOrder.createdAt ? new Date(latestOrder.createdAt).getTime() : null);
          console.log('✅ Found order pincode:', orderPincode);
          console.log('Order processedAt:', latestOrder.processedAt);
          console.log('Order createdAt:', latestOrder.createdAt);
          console.log('Converted timestamp:', orderTimestamp);
          console.log('Converted date:', orderTimestamp ? new Date(orderTimestamp).toISOString() : 'null');
        } else {
          console.log('❌ No shipping address pincode found in order - checked zip, postalCode, zipCode, pincode fields');
        }
      } else {
        
        console.log('Order conditions not met:', {
          currentUserId,
          ordersLoading,
          orderListLength: orderList?.length,
          hasOrderList: !!orderList
        });
      }

      console.log('Decision making:');
      console.log('- storedPincodeData:', storedPincodeData);
      console.log('- orderPincode:', orderPincode);
      console.log('- orderTimestamp:', orderTimestamp);

      // Enhanced debugging for timestamp comparison
      if (storedPincodeData && orderPincode && orderTimestamp) {
        console.log('🔍 TIMESTAMP COMPARISON DEBUG:');
        console.log('Order timestamp (raw):', orderTimestamp);
        console.log('Order timestamp (date):', new Date(orderTimestamp).toISOString());
        console.log('Saved timestamp (raw):', storedPincodeData.timestamp);
        console.log('Saved timestamp (date):', new Date(storedPincodeData.timestamp).toISOString());
        console.log('Order > Saved?', orderTimestamp > storedPincodeData.timestamp);
        console.log('Difference (ms):', orderTimestamp - storedPincodeData.timestamp);
        console.log('Difference (hours):', (orderTimestamp - storedPincodeData.timestamp) / (1000 * 60 * 60));
      }

      // Compare timestamps to determine which pincode is more recent - ALWAYS use the latest one
      if (storedPincodeData && orderPincode) {
        // Both exist, compare timestamps and use the most recent one
        if (orderTimestamp && orderTimestamp > storedPincodeData.timestamp) {
          // Order pincode is more recent
          pincodeToUse = orderPincode;
          source = 'latest order (more recent)';
          console.log('✅ Using order pincode as it is more recent:', pincodeToUse);
          console.log('Order timestamp:', new Date(orderTimestamp).toISOString());
          console.log('Saved timestamp:', new Date(storedPincodeData.timestamp).toISOString());
          console.log('Time difference (minutes):', (orderTimestamp - storedPincodeData.timestamp) / (1000 * 60));
        } else if (orderTimestamp && orderTimestamp < storedPincodeData.timestamp) {
          // Saved pincode is more recent
          pincodeToUse = storedPincodeData.pincode;
          source = 'local storage (more recent)';
          console.log('✅ Using stored pincode as it is more recent:', pincodeToUse);
          console.log('Order timestamp:', new Date(orderTimestamp).toISOString());
          console.log('Saved timestamp:', new Date(storedPincodeData.timestamp).toISOString());
          console.log('Time difference (minutes):', (storedPincodeData.timestamp - orderTimestamp) / (1000 * 60));
        } else if (orderTimestamp && orderTimestamp === storedPincodeData.timestamp) {
          // Timestamps are exactly equal, prefer order pincode
          pincodeToUse = orderPincode;
          source = 'latest order (same timestamp, preferring order)';
          console.log('✅ Using order pincode as timestamps are equal:', pincodeToUse);
          console.log('Timestamp:', new Date(orderTimestamp).toISOString());
        } else {
          // No valid order timestamp, use stored pincode
          pincodeToUse = storedPincodeData.pincode;
          source = 'local storage (no valid order timestamp)';
          console.log('✅ Using stored pincode (no valid order timestamp):', pincodeToUse);
        }
      } else if (storedPincodeData) {
        // Only stored pincode exists
        pincodeToUse = storedPincodeData.pincode;
        source = 'local storage';
        console.log('✅ Using stored pincode (only option):', pincodeToUse);
      } else if (orderPincode) {
        // Only order pincode exists
        pincodeToUse = orderPincode;
        source = 'latest order';
        console.log('✅ Using order pincode (only option):', pincodeToUse);
      } else {
        // Neither exists, use default
        pincodeToUse = '560103';
        source = 'default';
        console.log('✅ No pincode found, using default pincode:', pincodeToUse);
      }

      // Only proceed if pincode is different from current pin or pin is empty
      if (pincodeToUse !== pin) {
        // Set the pincode in the input field
        setPin(pincodeToUse);

        // Save to local storage if it came from order (to maintain timestamp tracking)
        // Do NOT save default pincode to storage
        if (source.includes('latest order') && source !== 'default') {
          await savePincodeToStorage(pincodeToUse);
        }

        // Show loading state and automatically trigger EDD check
        setLoading(true);
        fetchRequestEDD(pincodeToUse);
      }
    } catch (error) {
      console.error('Error prefilling pincode:', error);
      setLoading(false);
    }
  };

  // State to track if we've already attempted to prefill
  const [hasPrefilled, setHasPrefilled] = useState(false);

  useEffect(() => {
    setLoading(false);
    // Reset prefill state when component mounts (new product)
    setHasPrefilled(false);
  }, []);

  // Effect to prefill pincode when user and orders are loaded (only once)
  useEffect(() => {
    if (currentUserId && !ordersLoading && !hasPrefilled) {
      prefillPincode();
      setHasPrefilled(true);
    }
  }, [currentUserId, ordersLoading, hasPrefilled]);

  // Add a method to manually refresh and re-evaluate pincode (useful after order placement)
  const refreshPincodeFromOrders = async () => {
    console.log('🔄 Manually refreshing pincode from orders...');
    setHasPrefilled(false);
    await prefillPincode(true); // Force refresh orders
    setHasPrefilled(true);
  };

  // Effect to prefill pincode for guest users (from local storage only)
  useEffect(() => {
    if (!currentUserId && !hasPrefilled) {
      prefillPincode();
      setHasPrefilled(true);
    }
  }, [currentUserId, hasPrefilled]);

  function handleDeliveryDetailbutton() {
    setLoading(true);

    if (!pin || pin.length !== 6) {
      setPinText('Please Enter a valid Pin');
      setLoading(false);
    } else {
      fetchRequestEDD(pin);
      setPinText('');
    }
  }

  const fetchRequestEDD = async (pinData) => {
    const response = await fetch(
      settings.edd_api_url,
      {
        method: 'POST',
        headers: {
          accept: 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          dropPincode: pinData,
          quantity: 1,
          variantsData: extractProductVariantId,
        }),
      },
    );

    const responseData = await response.json();
    console.log(responseData, " response data")
    if (responseData.serviceable) {
      if (responseData?.variantsData?.length)
        setFinalEdd(responseData?.variantsData?.[0]?.edd);
      setPinText('');
      setLoading(false);

      // Save pincode to storage only when it's valid and serviceable
      // Do NOT save default pincode (560103) to storage
      if (pinData && pinData.length === 6 && pinData !== '560103') {
        savePincodeToStorage(pinData);
        console.log('Pincode saved after successful validation:', pinData);
      } else if (pinData === '560103') {
        console.log('Default pincode validated but not saved to storage:', pinData);
      }
    } else {
      setLoading(false);
      setFinalEdd('');
      setPinText('Pincode not serviceable.');
    }
  };

  return (
    <View>
      <View
        style={{
          borderWidth: 0,
          elevation: 0,
          paddingVertical: heightPixel(8),
          borderRadius: widthPixel(10),
        }}>
        <Text
          style={{
            color: '#221f20',
            fontSize: fonts._16,
            fontFamily: fonts.FONT_FAMILY.SemiBold,
            marginBottom: heightPixel(10),
          }}>
          Delivery Details
        </Text>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            overflow: 'hidden',
            gap: widthPixel(8),
            flexWrap: 'wrap',
          }}>
          <View style={{ width: '60%' }}>
            <TextInput
              placeholder="Enter Your pincode"
              maxLength={6}
              keyboardType="numeric"
              onChangeText={(newPin) => {
                if (newPin === '') {
                  setPinText('');
                }
                const trimmedPin = newPin.trim();
                setPin(trimmedPin);
              }}
              value={pin}
              style={{
                borderWidth: 1,
                borderColor: 'rgba(34,31,32,.1)',
                padding: widthPixel(8),
                borderRadius: widthPixel(8),
                fontFamily: 'Jost-SemiBold',
                paddingLeft: widthPixel(20),
                height: heightPixel(50),
                alignItems: 'center', justifyContent: 'center',
              }}
            />
          </View>
          <View style={{ width: '32%' }}>
            <Pressable
              onPress={() => {
                handleDeliveryDetailbutton();
              }}>
              <LinearGradient
                colors={['#221f20', '#505050']}
                start={{ x: -0.3336, y: 0 }} // Adjusted based on the angle in the CSS
                end={{ x: 1.3952, y: 1 }} // Adjusted based on the angle in the CSS
                style={{
                  paddingHorizontal: widthPixel(10),
                  // paddingVertical: heightPixel(13),
                  height: heightPixel(50),
                  alignItems: 'center', justifyContent: 'center',
                  borderRadius: widthPixel(10),
                }}>
                {!loading ? (
                  <Text style={styles.buttonText}>CHECK</Text>
                ) : (
                  <View
                    style={{
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}>
                    {/* <Spinner /> */}
                    <ActivityIndicator size={'small'} color={'white'} />
                  </View>
                )}
              </LinearGradient>
            </Pressable>
          </View>
        </View>

        {pinText != '' && (
          <View style={{ paddingVertical: heightPixel(10) }}>
            <Text
              style={{ color: 'red', fontFamily: fonts.FONT_FAMILY.Regular }}>
              {pinText}
            </Text>
          </View>
        )}

        {/* EDD details , div needs to be visible when pincode entered  */}
        {finalEdd ? (
          <View>
            <ScrollView
              horizontal={true}
              style={{
                paddingVertical: heightPixel(10),
                borderWidth: 0,
                marginVertical: heightPixel(10),
                width: '100%',
              }}
              contentContainerStyle={{
                paddingRight: widthPixel(50),
              }}>
              <View style={{ display: 'flex', flexDirection: 'row' }}>
                <View
                  style={{
                    padding: widthPixel(10),
                    borderRadius: widthPixel(7),
                    borderWidth: 1,
                    borderColor: 'rgba(34,31,32,.1)',
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    width: '60%',
                    marginRight: widthPixel(10),
                    height: heightPixel(50),
                  }}>
                  <View
                    style={{ width: widthPixel(20), height: widthPixel(20) }}>
                    <Image
                      source={{
                        uri: 'https://www.jockey.in/cdn/shop/files/Group_239103.jpg?v=16732760107086212298',
                      }}
                      style={{
                        width: '100%',
                        height: '100%',
                        resizeMode: 'contain',
                      }}
                    />
                  </View>
                  <View>
                    <Text
                      style={{
                        fontSize: fonts._14,
                        fontFamily: fonts.FONT_FAMILY.Regular,
                      }}>
                      Estimated Delivery by{' '}
                      <Text
                        style={{
                          fontSize: fonts._13,
                          fontFamily: fonts.FONT_FAMILY.Bold,
                        }}>
                        {finalEdd}
                      </Text>
                    </Text>
                  </View>
                </View>

                {/* 3 */}
                <View
                  style={{
                    padding: widthPixel(10),
                    borderRadius: widthPixel(7),
                    borderWidth: 1,
                    borderColor: 'rgba(34,31,32,.1)',
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    width: '40%',
                  }}>
                  <View
                    style={{ width: widthPixel(20), height: widthPixel(20) }}>
                    <Image
                      source={{
                        uri: 'https://www.jockey.in/cdn/shop/files/Vector_1_a29858f8-e63b-4de4-9be6-3f78849099e3.png?v=9477741078544455416',
                      }}
                      style={{
                        width: '100%',
                        height: '100%',
                        resizeMode: 'contain',
                      }}
                    />
                  </View>
                  <Text
                    style={{
                      fontSize: fonts._13,
                      fontFamily: fonts.FONT_FAMILY.Regular,
                    }}>
                    {'Eligible for Free Delivery'}
                  </Text>
                </View>
              </View>
            </ScrollView>
          </View>
        ) : (
          <View></View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  buttonText: {
    fontSize: fonts._14,
    color: 'white',
    textAlign: 'center',
    fontFamily: fonts.FONT_FAMILY.Regular,
  },
});
