import React, { useState, useEffect, useRef, useMemo } from 'react';
import {
  View,
  Image,
  TouchableOpacity,
  StyleSheet,
  Text,
  Dimensions,
  ScrollView,
  Animated,
  Pressable,
  Button,
  FlatList,
  Platform,
  TouchableWithoutFeedback,
} from 'react-native';
import Swiper from 'react-native-swiper';
import LinearGradient from 'react-native-linear-gradient';
import Svg, { Path, Polygon } from 'react-native-svg';
import { useDataSourceV2 } from '@appmaker-xyz/core';
import HomeFilterButton from '../common/HomeFilterButton';
import { useProducts } from '@appmaker-xyz/shopify'
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { RFPercentage } from 'react-native-responsive-fontsize';
import { fonts, getVerticalPadding, getHorizontalPadding, heightPixel, widthPixel } from '../../styles';
import FastImage from 'react-native-fast-image';
import ShopifyImage from '../ShopifyImage';
import Carousel from 'react-native-snap-carousel';
import Ripple from 'react-native-material-ripple';
import { Modal } from 'react-native';
import { Easing } from 'react-native-reanimated';
import ProductModal from '../common/ProductModal';
import { runDataSource } from '@appmaker-xyz/core';
import { currencyHelper, useProductDetail } from '@appmaker-xyz/shopify';
import { SvgUri } from 'react-native-svg';
import NewArrivalImageSlider from '../common/NewArrivalImageSlider';

export default function NewArrival1(props) {
  const { attributes, onAction, innerBlocks } = props;
  const setHeadingDefault =
    attributes?.selectDefaultButtom === 'tag1'
      ? attributes?.tag1?.toLowerCase()
      : attributes?.tag2?.toLowerCase();

  const arrayOfGids = useMemo(
    () => innerBlocks
      .map((block) => block?.attributes?.product)
      .filter((product) => product !== undefined),
    [innerBlocks]
  );

  const [combinedData, setCombinedData] = useState([]);
  const [categoryIndices, setCategoryIndices] = useState({
    [data1]: 0,
    [data2]: 0,
  });

  // const [activeIndex, setActiveIndex] = useState(0);
  // const [activeCategory, setActiveCategory] = useState(setHeadingDefault);

  const [productData, setProductData] = useState([]);
  const [productFitAndFeel, setProductFitAndFeel] = useState([]);
  const [activeCategory, setActiveCategory] = useState(setHeadingDefault);
  const [animationTriggered, setAnimationTriggered] = useState(true);
  const [isModal, setIsModal] = useState(false);
  const [activeIndex, setActiveIndex] = useState(0);
  const [modalImgActiveIndex, setModalImgActiveIndex] = useState(0);
  const [isAddToCartModalVisible, setIsAddToCartModalVisible] = useState(false);
  const { width: windowWidth } = Dimensions.get('window');
  const [isTransitioning, setIsTransitioning] = useState(false);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const carouselRef = useRef(null);
  const modalCarouselRef = useRef(null);
  const currentRequestId = useRef(0);
  const imageAnim = useRef(new Animated.Value(0)).current;
  const translateY = useRef(new Animated.Value(50)).current;
  const opacity1 = useRef(new Animated.Value(0)).current;
  const scaleAnimModal = useRef(new Animated.Value(0.5)).current;
  const opacityAnimModal = useRef(new Animated.Value(0)).current;
  const isTapTriggered = useRef(false);
  const [displayedImageUrl, setDisplayedImageUrl] = useState(null);

  const dataSource = {
    attributes: {},
    source: 'shopify',
  };
  // const productIds = ['gid://shopify/Product/8804664803627', 'gid://shopify/Product/8804664803627']
  // const { productList } = useProducts({ query: { productIds } });
  // console.log("JSON.stringify(productList)Olld", JSON.stringify(productList));
  // return
  async function getAndUseShopifyProducts(arrayOfGids) {

    try {
      const formattedIds = arrayOfGids?.map(id => `"${id}"`).join(', ');
      async function fetchShopifyProducts() {
        const [response] = await runDataSource(
          { dataSource },
          {
            methodName: 'gqlQuery',
            params: {
              query: `
              {
                nodes(ids: [${formattedIds}]) {
                  ... on Product {
                    id
                    title
                    tags
                    handle
                    totalInventory
                    description
                    descriptionHtml
                    onlineStoreUrl
                    productType
                    vendor
                    availableForSale

                    media(first: 10) {
                      edges {
                        node {
                          mediaContentType
                          alt
                          ... on MediaImage {
                            image {
                              src
                            }
                          }
                        }
                      }
                    }

                    priceRange {
                      minVariantPrice {
                        amount
                        currencyCode
                      }
                      maxVariantPrice {
                        amount
                        currencyCode
                      }
                    }

                    compareAtPriceRange {
                      minVariantPrice {
                        amount
                        currencyCode
                      }
                      maxVariantPrice {
                        amount
                        currencyCode
                      }
                    }

                    options {
                      name
                      values
                    }

                    appmaker_hide_product_status: metafield(
                      namespace: "appmaker", 
                      key: "hide_product_status"
                    ) {
                      value
                    }

                    variants(first: 10) {
                      edges {
                        node {
                          id
                          title
                          price {
                            amount
                            currencyCode
                          }
                        }
                      }
                    }

                    images(first: 10) {
                      edges {
                        node {
                          originalSrc
                          altText
                        }
                      }
                    }

                    product_name_for_collection_title: metafield(
                      namespace: "custom", 
                      key: "product_name_for_collection_title"
                    ) {
                      value
                    }

                    product_type: metafield(
                      namespace: "custom", 
                      key: "product_type"
                    ) {
                      value
                    }

                    color_variant: metafield(
                      namespace: "custom", 
                      key: "color_variant"
                    ) {
                      value
                    }

                    product_color_name: metafield(
                      namespace: "custom", 
                      key: "product_color_name"
                    ) {
                      value
                    }

                    style_number: metafield(
                      namespace: "custom", 
                      key: "style_number"
                    ) {
                      value
                    }

                    colour_family_metafield: metafield(
                      namespace: "custom", 
                      key: "colour_family_metafield"
                    ) {
                      value
                    }

                    filter_colour: metafield(
                      namespace: "custom", 
                      key: "filter_colour"
                    ) {
                      value
                    }

                    select_size_guide: metafield(
                      namespace: "custom", 
                      key: "select_size_guide"
                    ) {
                      value
                    }

                    pack_of_product_to_add: metafield(
                      namespace: "custom", 
                      key: "pack_of_product_to_add"
                    ) {
                      value
                    }

                    pack_of_product_number: metafield(
                      namespace: "custom", 
                      key: "pack_of_product_number"
                    ) {
                      value
                    }

                    uom_quantity: metafield(
                      namespace: "custom", 
                      key: "uom_quantity"
                    ) {
                      value
                    }

                    product_fit_and_feel: metafield(
                      namespace: "custom", 
                      key: "product_fit_and_feel"
                    ) {
                      __typename
                      value
                    }

                    product_fit_and_feel_image: metafield(
                      namespace: "custom", 
                      key: "product_fit_and_feel_image"
                    ) {
                      __typename
                      value
                    }
                  }
                }
              }
            `
            }
          }
        );

        return response?.data?.data || [];
      }

      const productData = await fetchShopifyProducts();
      setProductData(productData)
      return productData;

    } catch (error) {
      console.error('Error adaddd :', error);
      return [];
    }
  }

  useEffect(() => {
    if (productData?.nodes) {
      const mergedData = innerBlocks.map((block) => {
        const matchingProduct = productData?.nodes?.find(
          (product) => product?.id === block?.attributes?.product,
        );

        return {
          ...block,
          productDetails: matchingProduct || {},
        };
      });

      setCombinedData(mergedData);
    }
  }, [productData, innerBlocks]);

  const data1 = attributes?.tag1?.toLowerCase();
  const data2 = attributes?.tag2?.toLowerCase();

  const data = {
    [data1]: combinedData.filter(
      (block) => block.attributes.selectCategory === 'tag1',
    ),
    [data2]: combinedData.filter(
      (block) => block.attributes.selectCategory === 'tag2',
    ),
  };

  useEffect(() => {
    const index = categoryIndices[activeCategory] || 0;

    if (carouselRef.current && productList1?.length > 0) {
      carouselRef.current.snapToItem(index, false);
    }
  }, [activeCategory, productList1]);


  useEffect(() => {

    getAndUseShopifyProducts(arrayOfGids)

  }, [])

  const productList1 = useMemo(() => data?.[activeCategory] || [], [data, activeCategory]);

  const fetchSingleImage = async (imageGid) => {
    try {
      const [response] = await runDataSource(
        { dataSource },
        {
          methodName: 'gqlQuery',
          params: {
            query: `
              {
                node(id: "${imageGid}") { 
                  ... on MediaImage {
                    id
                    image {
                      src
                      height
                      altText
                      width
                    }
                  }
                }
              }
            `
          }
        }
      );
      return response || null;
    } catch (error) {
      console.error('Error fetching single image:', error);
      return null;
    }
  };

  const contentContainerStyle = {
    ...attributes?.containerStyles,
  };

  const productIds = [productList1?.[activeIndex]?.productDetails?.id]
  const { productList } = useProducts({ query: { productIds } });

  const Value = productList1?.[activeIndex]?.productDetails?.style_number;
  const title = productList1?.[activeIndex]?.productDetails?.title;
  const images = productList1?.[activeIndex]?.productDetails?.media?.edges;
  const mainImageUrl = productList1?.[activeIndex]?.productDetails?.images?.edges?.[4]?.node?.originalSrc ?? productList1?.[activeIndex]?.productDetails?.images?.edges?.[0]?.node?.originalSrc
  const handle = productList1?.[activeIndex]?.productDetails?.handle

  const getImageUrlForIndex = (index) => {
    return (
      productList1?.[activeIndex]?.attributes?.productimage?.url ??
      productList1?.[index]?.productDetails?.images?.edges?.[0]?.node?.originalSrc
    );
  }

  // useEffect(() => {
  //   if (productList1) {
  //     const imageUrl = getImageUrlForIndex(activeIndex);
  //     if (imageUrl) setDisplayedImageUrl(imageUrl);
  //   }
  // }, []);


  useEffect(() => {
    if (isModal && modalCarouselRef.current) {
      modalCarouselRef.current?.snapToItem(modalImgActiveIndex, true);
    }
  }, [isModal])


  useEffect(() => {
    const index = categoryIndices[activeCategory] || 0;

    if (carouselRef.current && productList1?.length > 0) {
      carouselRef.current.snapToItem(index, false);
    }
  }, [activeCategory, categoryIndices]);

  useEffect(() => {
    const product = data?.[activeCategory]?.[activeIndex];
    if (product) {
      buildProductFitAndFeel();
    }
  }, [data, activeCategory, activeIndex]);

  useEffect(() => {
    const nextImageUrl = getImageUrlForIndex(activeIndex);

    if (nextImageUrl === displayedImageUrl || isTransitioning) return;

    setIsTransitioning(true);

    Animated.timing(imageAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setDisplayedImageUrl(nextImageUrl);
      imageAnim.setValue(1);
      Animated.timing(imageAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start(() => {
        setIsTransitioning(false);
      });
    });
  }, [activeIndex, displayedImageUrl, isTransitioning, activeCategory]);


  useEffect(() => {
    triggerAnimation()
  }, [activeCategory])

  const openModal = () => {
    setIsModal(true);
    Animated.parallel([
      Animated.timing(scaleAnimModal, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnimModal, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const closeModal = () => {
    Animated.parallel([
      Animated.timing(scaleAnimModal, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnimModal, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setIsModal(false);
    });
  };

  const buildProductFitAndFeel = async () => {
    const thisRequestId = ++currentRequestId.current;
    const rawValue = productList1?.[activeIndex]?.productDetails?.product_fit_and_feel?.value;
    const rawImages = productList1?.[activeIndex]?.productDetails?.product_fit_and_feel_image?.value;

    let htmlArray = [];
    let imageGids = [];

    try {
      htmlArray = JSON.parse(rawValue || '[]');
    } catch (e) {
      console.error("Invalid JSON in product_fit_and_feel");
    }

    try {
      imageGids = JSON.parse(rawImages || '[]');
    } catch (e) {
      console.error("Invalid JSON in product_fit_and_feel_image");
    }

    try {
      const imageUrls = await Promise.all(
        imageGids.map(async (gid) => {
          const res = await fetchSingleImage(gid);
          return res?.data?.data?.node?.image?.src || null;
        })
      )

      if (thisRequestId !== currentRequestId.current) return

      const result = htmlArray.map((item, index) => {
        const h3Match = item.match(/<h3>(.*?)<\/h3>/);
        const pMatch = item.match(/<p>(.*?)<\/p>/);

        return {
          title: h3Match ? h3Match[1] : null,
          description: pMatch ? pMatch[1] : null,
          image: imageUrls[index] || null,
        };
      });

      setProductFitAndFeel(result);
    } catch (error) {
      console.error("Error in buildProductFitAndFeel:", error);
    }
  };

  const handleImagePress = (index) => {
    modalCarouselRef.current?.snapToItem(index, true);
  };

  const onCarouselImageChange = (index) => {
    if (index === activeIndex) return;

    if (isTapTriggered.current) {
      carouselRef.current?.snapToItem(index, true);
    }

    setActiveIndex(index);
    setCategoryIndices((prev) => ({
      ...prev,
      [activeCategory]: index,
    }));

    triggerAnimation();
    isTapTriggered.current = false;
  };


  const triggerAnimation = () => {
    fadeAnim.setValue(0);
    scaleAnim.setValue(0.8);

    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setAnimationTriggered(true);
    });
  };


  const sideImages = () => {

    return (
      <View style={{ position: "absolute", zIndex: 1, left: widthPixel(6) }} >

        <Text style={{ color: "black" }} >Other images</Text>

        {getVerticalPadding(14)}

        <>
          {images?.slice(0, 3).map((item, index) => {

            return (

              <Ripple
                onPress={() => { setModalImgActiveIndex(index), setIsModal(true), openModal() }}
                style={{
                  height: heightPixel(54),
                  width: widthPixel(54),
                  borderRadius: widthPixel(6),
                  borderWidth: 1,
                  borderColor: "white",
                  marginBottom: widthPixel(10),
                }} >
                <Animated.View
                  key={index}
                  style={{
                    height: heightPixel(54),
                    width: widthPixel(54),
                    borderRadius: widthPixel(6),
                    borderWidth: 1,
                    borderColor: "white",
                    marginBottom: widthPixel(10),
                    opacity: animationTriggered ? fadeAnim : 1,
                    transform: [
                      { scale: animationTriggered ? scaleAnim : 1 },
                    ],
                  }}
                >
                  <ShopifyImage
                    source={{ uri: item?.node?.image?.src }}
                    style={{ height: "100%", width: "100%", borderRadius: widthPixel(6) }}
                    maxWidth={200}
                    resizeMode={"contain"}
                  />
                </Animated.View>

              </Ripple>
            )
          })}
        </>

      </View>
    )

  }

  const handleCategoryButtonClick = (category) => {
    if (activeCategory !== category) {
      const newIndex = categoryIndices[category] || 0;
      setActiveCategory(category);
      setActiveIndex(newIndex);

      if (!categoryIndices[category]) {
        setCategoryIndices((prev) => ({
          ...prev,
          [category]: 0,
        }));
      }

      Animated.timing(opacity1, {
        toValue: 0,
        duration: 100,
        useNativeDriver: true,
      }).start(() => {
        setCategoryIndices((prev) => ({
          ...prev,
          [category]: newIndex,
        }));
      });
    }
  };

  const toggleModal = () => {
    setIsAddToCartModalVisible(!isAddToCartModalVisible);
  };

  return (
    // <LinearGradient
    // colors={[
    // 'rgba(36, 43, 60, 1)',
    // 'rgba(122, 126, 137, 1)',
    // 'rgba(22, 28, 42, 1)',
    // ]}
    // start={{ x: 0.2, y: 0 }}
    // end={{ x: 0.1, y: 1 }}
    // locations={[0, 0.4948, 1]}
    // style={{ paddingBottom: 24 }}>
    <View style={{ backgroundColor: "#F0F0F0", paddingBottom: heightPixel(30) }} >

      {
        isAddToCartModalVisible &&
        <ProductModal
          modalVisible={isAddToCartModalVisible}
          toggleModal={toggleModal}
          handle={handle}
          productVariantDetails={productList?.[0].node?.color_variant?.references}
          props={props}
        // addToCart={addToCart}
        />
      }

      <View style={[styles.newArrivalContainer, contentContainerStyle]}>
        <View style={styles.header}>
          <Text style={[{ color: "black", fontSize: fonts._30, fontFamily: "Jost-Bold", fontWeight: "700", lineHeight: heightPixel(52) }, { letterSpacing: 2.56 }]}>
            NEW <Text style={{ color: "#221f20c7", fontFamily: "Jost-Medium", fontWeight: "500", lineHeight: heightPixel(52) }} >ARRIVALS</Text>
          </Text>
        </View>

        {getVerticalPadding(6)}

        <View style={{ zIndex: 10 }} >

          <HomeFilterButton
            activeCategory={activeCategory}
            setActiveCategory={setActiveCategory}
            handleCategoryButtonClick={handleCategoryButtonClick}
            attributes={attributes}
            borderColor={'rgba(34, 31, 32, .35)'}
            boxbackgroundColor={'white'}
            buttonTextColor={'black'}
            buttonBackgroundColor={'transparent'}
            activeButtonTextColor={'white'}
            activeButtonBackgroundColor={'black'}
            fontFamily={'Jost-Regular'}
            isWhiteBg
          />
        </View>

        <View style={{ paddingHorizontal: widthPixel(6) }} >

          {sideImages()}

          <View style={{ height: heightPixel(365), justifyContent: "center", alignItems: "center" }}>
            <Animated.View
              key={productList1}
              style={{
                // height: heightPixel(350),
                width: windowWidth * 0.6,
                opacity: imageAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [1, 0],
                }),
                transform: [
                  {
                    translateY: imageAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0, 50],
                    }),
                  },
                ],
                zIndex: -10,
                // marginTop: heightPixel(-50),
              }}
            >
              <ShopifyImage
                key={displayedImageUrl}
                source={{ uri: displayedImageUrl }}
                style={{ height: heightPixel(320), width: '100%', transform: [{ scale: 1.1 }] }}
                resizeMode="contain"
              />
            </Animated.View>
          </View>

        </View>

        <View
          style={{ width: "100%", marginTop: heightPixel(-70) }}
        >

          <NewArrivalImageSlider
            carouselRef={carouselRef}
            productList1={data?.[activeCategory]}
            // mainImage={data?.[activeCategory] || []}
            key={activeCategory}
            windowWidth={windowWidth}
            activeIndex={activeIndex}
            setActiveIndex={setActiveIndex}
            triggerAnimation={triggerAnimation}
            onCarouselImageChange={onCarouselImageChange}
            isTapTriggered={isTapTriggered}
          />

        </View>

        <View style={{
          width: widthPixel(105),
          marginLeft: widthPixel(40),
        }} >
          <View style={{
            width: widthPixel(105), alignItems: "center", position: "absolute",
          }} >
            <Svg
              height="30"
              width="40"
              viewBox="0 33.299 490 423.402"
              fill="#2a2a2a"
              stroke="#2a2a2a"
              style={{ transform: [{ rotate: '180deg' }] }}
            >
              <Polygon points="245,456.701 490,33.299 0,33.299" />
            </Svg>

          </View>
        </View>



        <View style={{ paddingHorizontal: widthPixel(6) }} >

          <View style={{
            paddingBottom: heightPixel(22),
            borderRadius: widthPixel(22),
            width: "100%",
            alignSelf: "center",
            backgroundColor: "white",
            marginTop: heightPixel(13),
          }} >

            <View style={{
              backgroundColor: "#2a2a2a",
              borderTopLeftRadius: widthPixel(22),
              borderTopRightRadius: widthPixel(22),
              paddingVertical: heightPixel(13),
              paddingHorizontal: widthPixel(19),
              flexDirection: "row",
            }} >


              <View style={{
                height: heightPixel(26),
                width: widthPixel(60),
                backgroundColor: "#635c5c",
                borderRadius: widthPixel(36),
                justifyContent: "center",
                alignItems: "center"
              }} >

                <Text style={{
                  fontSize: fonts._12,
                  fontFamily: "Jost-Regular",
                  fontWeight: "400",
                  color: "white",

                }} >New</Text>
              </View>

              {getHorizontalPadding(35)}

              <View
                style={{ maxWidth: widthPixel(200), height: "100%" }}
              >

                <Text style={{ fontFamily: "Jost-Regular", fontWeight: "500", fontSize: fonts._12, color: "white", lineHeight: 0 }} >

                  {Value?.value}

                </Text>

                <Text style={{
                  fontSize: fonts._12,
                  fontFamily: "Jost-Regular",
                  fontWeight: "400",
                  color: "white",
                }}
                  numberOfLines={3}
                  ellipsizeMode="tail"
                >{title}</Text>

              </View>

            </View>

            <View style={{ padding: widthPixel(20), flexDirection: "row", borderColor: "#2a2a2a", justifyContent: "space-between", alignItems: "center" }} >

              <Text style={{ color: "#221f20", fontWeight: "600", fontFamily: "Jost-Bold", fontSize: fonts._14 }} >{currencyHelper(
                productList1?.[activeIndex]?.productDetails?.priceRange?.minVariantPrice?.amount,
                productList1?.[activeIndex]?.productDetails?.priceRange?.minVariantPrice?.currencyCode,
              )}</Text>

              <View style={{ flexDirection: "row", gap: widthPixel(20) }} >

                <Ripple
                  onPress={() => {
                    // data[activeCategory]?.[activeIndex]?.attributes?.appmakerAction
                    // ? onAction(data[activeCategory]?.[activeIndex]?.attributes?.appmakerAction)
                    props.onAction({
                      action: 'OPEN_PRODUCT',
                      params: {
                        productHandle: `${productList1?.[activeIndex]?.productDetails?.handle}`,
                        replacePage: false,
                      },
                    });
                  }}
                  style={{ width: widthPixel(126), height: heightPixel(44), borderWidth: 2, borderRadius: widthPixel(6), justifyContent: "center", alignItems: "center" }} >

                  <Text style={{ fontSize: fonts._12, fontWeight: "500", fontFamily: "Jost-Medium" }} >EXPLORE</Text>

                </Ripple>

                <Ripple onPress={() => setIsAddToCartModalVisible(true)} style={{ height: heightPixel(44), width: widthPixel(45), backgroundColor: "#221f20", borderRadius: widthPixel(10), justifyContent: "center", alignItems: "center" }} >

                  <Svg width={18} height={19} viewBox="0 0 18 19" fill="none" {...props}>
                    <Path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M11.2988 3.93606C11.2988 1.90046 9.60755 0.231445 7.5405 0.231445C5.47345 0.231445 3.7823 1.90053 3.7823 3.93593V5.46251H0.859289C0.504293 5.46251 0.23291 5.72707 0.23291 6.07314V15.6398C0.23291 17.3293 1.63178 18.693 3.3648 18.693H8.7463C8.52841 18.3161 8.34935 17.9139 8.21458 17.4919L3.36452 17.492C2.3205 17.492 1.48538 16.6778 1.48538 15.6601V6.70401L3.7823 6.70387V7.76225C3.7823 8.10833 4.05368 8.37289 4.40867 8.37289C4.76367 8.37289 5.03505 8.10833 5.03505 7.76225V6.70387H10.0461V7.76225C10.0461 8.10833 10.3175 8.37289 10.6725 8.37289C11.0275 8.37289 11.2988 8.10833 11.2988 7.76225V6.70387H13.5956V9.48248C13.7547 9.47015 13.9155 9.46387 14.0778 9.46387C14.3388 9.46387 14.5959 9.48011 14.8483 9.51163V6.07314C14.8483 5.72707 14.5769 5.46251 14.2219 5.46251L11.2988 5.46265V3.93606ZM7.5405 1.45243C8.91853 1.45243 10.046 2.57202 10.046 3.93572V5.4623H5.03498V3.93572C5.03498 2.57188 6.16246 1.45243 7.5405 1.45243Z"
                      fill="#fff"
                    />
                    <Path
                      d="M14.0773 12.5402C13.6997 12.5402 13.3936 12.8462 13.3936 13.2239V14.9334H11.6841C11.3065 14.9334 11.0004 15.2394 11.0004 15.6171C11.0004 15.9947 11.3065 16.3008 11.6841 16.3008H13.3936V18.0103C13.3936 18.3879 13.6997 18.694 14.0773 18.694C14.455 18.694 14.761 18.3879 14.761 18.0103V16.3008H16.4705C16.8482 16.3008 17.1542 15.9947 17.1542 15.6171C17.1542 15.2394 16.8482 14.9334 16.4705 14.9334H14.761V13.2239C14.761 12.8462 14.455 12.5402 14.0773 12.5402Z"
                      fill="#fff"
                    />
                  </Svg>

                </Ripple>

              </View>

            </View>

            <View style={{ paddingHorizontal: widthPixel(22) }} >

              <ScrollView
                key={Platform.OS === 'android' ? activeIndex : undefined}
                horizontal={true}
                showsHorizontalScrollIndicator={false}
                scrollEnabled={true}
                style={{
                  height: heightPixel(64),
                }}
                contentContainerStyle={{
                  flexDirection: "row",
                }}
              >
                <View style={{ flexDirection: "row", width: "100%", gap: 20 }}>

                  {productFitAndFeel?.map((item, index) => {
                    return (
                      <View
                        style={{
                          height: heightPixel(60),
                          minWidth: widthPixel(210),
                          padding: widthPixel(10),
                          gap: widthPixel(10),
                          flexDirection: "row",
                          borderWidth: 1,
                          borderColor: "rgba(34, 31, 32, .11)",
                          borderRadius: widthPixel(10),
                          marginBottom: heightPixel(10),
                        }}
                        key={index}
                      >
                        {item?.image?.includes('.svg') ? (
                          <SvgUri
                            width={widthPixel(36)}
                            height={widthPixel(36)}
                            uri={item?.image}
                          />
                        ) : (
                          <Image
                            source={{ uri: item?.image }}
                            style={{
                              width: widthPixel(36),
                              height: widthPixel(36),
                              resizeMode: 'cover',
                            }}
                          />
                        )}
                        <View>
                          <Text
                            style={{
                              fontSize: fonts._14,
                              color: "#221f20",
                              fontWeight: "500",
                              fontFamily: "Jost-Medium"
                            }}
                          >
                            {item?.title}
                          </Text>
                          <Text
                            style={{
                              fontSize: fonts._12,
                              color: "#221f2099",
                              fontWeight: "400",
                              fontFamily: "Jost-Medium"
                            }}
                          >
                            {item?.description}

                          </Text>
                        </View>

                      </View>
                    );
                  })}

                </View>
              </ScrollView>

            </View>


          </View>

        </View>


      </View>

      <Modal visible={isModal} transparent animationType="fade">
        <View style={{ flex: 1, backgroundColor: "rgba(0, 0, 0, 0.5)", justifyContent: "center", alignItems: "center" }}>

          <TouchableWithoutFeedback onPress={closeModal}>
            <View style={{ position: 'absolute', top: 0, bottom: 0, left: 0, right: 0 }} />
          </TouchableWithoutFeedback>

          <Animated.View
            style={{
              backgroundColor: "#F0F0F0",
              height: heightPixel(400),
              width: "90%",
              borderRadius: widthPixel(16),
              alignItems: "center",
              justifyContent: "center",
              transform: [{ scale: scaleAnimModal }],
              opacity: opacityAnimModal,
              zIndex: 2,
            }}
          >

            <View
              style={{
                backgroundColor: "#F0F0F0",
                borderRadius: widthPixel(16),
                alignItems: "center",
                justifyContent: "center",
                paddingBottom: heightPixel(20),
              }}
            >
              <Ripple
                onPress={() => {
                  closeModal();
                }}
                style={{
                  position: "absolute",
                  height: widthPixel(30),
                  width: widthPixel(30),
                  borderRadius: widthPixel(50),
                  backgroundColor: "white",
                  top: 20,
                  right: 20,
                  justifyContent: "center",
                  alignItems: "center",
                  zIndex: 5,
                }}
              >
                <Svg width={20} height={20} viewBox="0 0 20 20" fill="none">
                  <Path
                    d="M14.875 14.875L5.125 5.125M14.875 5.125L5.125 14.875"
                    stroke="#221F20"
                    strokeWidth={2}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </Svg>
              </Ripple>

              <Carousel
                ref={modalCarouselRef}
                data={images?.slice(0, 4)}
                itemWidth={widthPixel(320)}
                sliderWidth={widthPixel(320)}
                onSnapToItem={(index) => { handleImagePress(index), setModalImgActiveIndex(index) }}
                firstItem={modalImgActiveIndex}
                contentContainerCustomStyle={{
                  paddingTop: heightPixel(10),
                }}
                renderItem={({ item }) => {
                  return (
                    <View style={{ borderRadius: widthPixel(9) }}>
                      <ShopifyImage
                        source={{ uri: item?.node?.image?.src }}
                        style={{
                          height: "100%",
                          width: "100%",
                          borderRadius: widthPixel(9),
                        }}
                        resizeMode={"contain"}
                      />
                    </View>
                  );
                }}
              />

              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-evenly",
                  height: heightPixel(70),
                  width: "100%",
                }}
              >
                {images?.slice(0, 4).map((item, index) => {
                  const isActive = index === modalImgActiveIndex;
                  return (
                    <Ripple
                      key={index}
                      onPressIn={() => handleImagePress(index)}
                      style={{
                        width: widthPixel(70),
                        height: heightPixel(75),
                        borderRadius: widthPixel(16),
                        borderWidth: 3,
                        borderColor: isActive ? "black" : "white",
                      }}
                    >
                      <ShopifyImage
                        source={{ uri: item?.node?.image?.src }}
                        style={{
                          height: "100%",
                          width: "100%",
                          borderRadius: widthPixel(16),
                        }}
                        resizeMode={"contain"}
                      />
                    </Ripple>
                  );
                })}
              </View>
            </View>
          </Animated.View>

        </View>
      </Modal>


      <View style={{
        height: heightPixel(32),
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center",
        gap: widthPixel(6),
      }} >

        {
          Array.isArray(productList1) && productList1.length > 0 && (
            productList1.map((item, index) => {
              const isActive = index === activeIndex;

              return (
                <Ripple
                  onPress={() => setActiveIndex(index)}
                  key={index}
                  style={{
                    height: widthPixel(13),
                    width: widthPixel(13),
                    borderRadius: widthPixel(100),
                    borderWidth: 1,
                    borderColor: isActive ? "black" : "transparent",
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  <View
                    style={{
                      height: widthPixel(6),
                      width: widthPixel(6),
                      backgroundColor: isActive ? "black" : "#221f205e",
                      borderRadius: widthPixel(100),
                    }}
                  />
                </Ripple>
              );
            })
          )
        }

      </View>



    </View >
    // </LinearGradient>
  );
}

const styles = StyleSheet.create({
  newArrivalContainer: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    paddingTop: 30,
  },
  exp: {
    // position: 'relative',
    // flex: 1,
    alignItems: 'center',
  },
  sliderContainer: {
    marginTop: 10,
    // width: width * 0.85,
    width: wp('85%'),
    alignSelf: 'center',
    borderWidth: 0,
    borderColor: '#ccc',
  },
  slide: {
    width: '100%',
    borderRadius: widthPixel(20),
    overflow: 'hidden',
    paddingHorizontal: widthPixel(5),
    alignItems: 'center',
    justifyContent: 'center'
  },
  image: {
    resizeMode: 'contain',
    // height: '100%',
    height: 470,
    width: 320,
    // width: '100%',
  },
  infoContainer: {
    position: 'absolute',
    bottom: 5,
    borderBottomLeftRadius: widthPixel(18),
    borderBottomRightRadius: widthPixel(18),
    // paddingHorizontal: 25,
    alignSelf: 'center',
    width: '80%'
    // height: hp('15%'),
  },
  titleContainer: {
    flex: 1,
  },
  titleWrapper: {
    // marginHorizontal: widthPixel(5),
    // marginVertical: 10,
  },
  title: {
    color: 'white',
    textAlign: 'left',
    // fontFamily: 'Jost-Regular',
    fontFamily: fonts.FONT_FAMILY.Regular,
    fontSize: fonts._16,
    fontWeight: 500,
    lineHeight: heightPixel(22),
  },
  bottomRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    // backgroundColor: 'black',
  },
  styleNumberContainer: {
    backgroundColor: '#f0f0f0',
    paddingVertical: 5,
    paddingHorizontal: widthPixel(10),
    borderRadius: widthPixel(55),
    backgroundColor: '#ffffff14',
    // marginBottom: 10,
  },
  styleNumber: {
    fontSize: fonts._12,
    color: '#fff',
    fontFamily: fonts.FONT_FAMILY.Medium,
  },
  exploreButton: {
    paddingVertical: 8,
    paddingHorizontal: widthPixel(24),
    borderRadius: widthPixel(5),
    backgroundColor: '#ffffff14',
    borderColor: 'white',
    borderWidth: 1,
  },
  exploreButtonText: {
    fontSize: fonts._12,
    color: '#fff',
    fontFamily: fonts.FONT_FAMILY.Medium,
    fontWeight: 500,
  },
  gradientLayer: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    zIndex: 2,
  },
  closeButton: {
    position: "absolute",
    top: 10,
    right: 10,
    zIndex: 10,
  },
});